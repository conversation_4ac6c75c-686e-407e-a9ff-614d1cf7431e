import React, { useState } from 'react';
import { useQuery, useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { GET_PEOPLE } from '../../../api/queries';
import { DELETE_PERSON } from '../../../api/mutations';
import CreatePersonModal from './CreatePerson';
import copyToClipboard from '../../utils/clipboard';
import '../../../css/shared.css';

export default function People() {
  const { loading, error, data, refetch } = useQuery(GET_PEOPLE);
  const [copiedId, setCopiedId] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const [deletePersonMutation] = useMutation(DELETE_PERSON, {
    onCompleted: () => {
      refetch();
      toast.success('Person deleted successfully!');
    },
    onError: (deleteError) => {
      console.error('Error deleting person:', deleteError);
      toast.error(`Error deleting person: ${deleteError.message}`);
    }
  });

  const handleDeletePerson = (personId, personName) => {
    if (window.confirm(`Are you sure you want to delete ${personName} (ID: ${personId})? This action cannot be undone.`)) {
      deletePersonMutation({ variables: { hiveId: personId } });
    }
  };


  if (loading) return <div style={{ padding: '1rem' }}>Loading people...</div>;
  if (error) return <div style={{ padding: '1rem' }}>Error: {error.message}</div>;

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  return (
    <div style={{ padding: '1rem' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h1>People</h1>
        <button onClick={openModal} className="button-primary">
          Create Person
        </button>
      </div>

      <CreatePersonModal isOpen={isModalOpen} onClose={closeModal} />

      <div style={{ marginTop: '2rem' }}>
        {data.people.map((person) => (
          <div 
            key={person.hiveId}
            style={{
              padding: '1rem',
              marginBottom: '1rem',
              borderRadius: '4px',
              backgroundColor: '#222',
              position: 'relative'
            }}
          >
            <button 
              onClick={() => handleDeletePerson(person.hiveId, `${person.firstName} ${person.lastName}`)}
              className="button-delete"
              style={{ position: 'absolute', top: '12px', right: '16px' }}
              title="Delete Person"
            >
              Delete
            </button>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginRight: '60px' }}>
              <h3>{`${person.firstName} ${person.lastName}`}</h3>
              <span 
                style={{ 
                  fontSize: '0.8em', 
                  color: '#909090',
                  cursor: 'pointer',
                  userSelect: 'none'
                }}
                onClick={() => copyToClipboard(person.hiveId, setCopiedId)}
                title="Click to copy ID"
              >
                {person.hiveId}
                {copiedId === person.hiveId && ' ✓'}
              </span>
            </div>
            <hr></hr>
            <div style={{ display: 'flex', gap: '1rem' }}>
              <div style={{ 
                flex: 1,
                padding: '1rem',
                backgroundColor: '#2a2a2a',
                borderRadius: '4px'
              }}>
                <div style={{marginBottom: '6px'}}>
                  <strong>Date of Birth:</strong> <p style={{color: '#909090'}}>{person.dateOfBirth ? new Date(person.dateOfBirth).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: '2-digit' }) : 'Not specified'}</p>
                </div>
              </div>
              <div style={{ 
                flex: 1,
                padding: '1rem',
                backgroundColor: '#2a2a2a',
                borderRadius: '4px'
              }}>
                <div>
                  <strong>Has Agent Profile:</strong>
                  {person.hasAgent && person.hasAgent.length > 0 ? (
                    <ul>
                      {person.hasAgent.map(agent => (
                        <li style={{marginLeft: '18px', color: '#ffda94'}} key={agent.hiveId}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            {agent.username}
                            <span 
                              style={{fontSize: '0.8em',color: '#909090',cursor: 'pointer',userSelect: 'none'}}
                              onClick={() => copyToClipboard(agent.hiveId, setCopiedId)}
                              title="Copy agent ID"
                            >
                              {agent.hiveId}
                              {copiedId === agent.hiveId && ' ✓'}
                            </span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p style={{color: '#909090'}}>Not an agent</p>
                  )}
                </div>
                <div style={{marginTop: '6px'}}>
                  <strong>Has Informant Profile:</strong>
                  {person.hasInformant && person.hasInformant.length > 0 ? (
                    <ul>
                      {person.hasInformant.map(informant => (
                        <li style={{marginLeft: '18px', color: '#ffda94'}} key={informant.hiveId}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            {informant.codeName}
                            <span 
                              style={{fontSize: '0.8em',color: '#909090',cursor: 'pointer',userSelect: 'none'}}
                              onClick={() => copyToClipboard(informant.hiveId, setCopiedId)}
                              title="Copy informant ID"
                            >
                              {informant.hiveId}
                              {copiedId === informant.hiveId && ' ✓'}
                            </span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p style={{color: '#909090'}}>Not an informant</p>
                  )}
                </div>
                <div style={{marginTop: '6px'}}>
                  <strong>Owns Vehicle(s):</strong>
                  {person.ownsVehicle && person.ownsVehicle.length > 0 ? (
                    <ul>
                      {person.ownsVehicle.map(vehicle => (
                        <li style={{marginLeft: '18px', color: '#B0E0E6'}} key={vehicle.hiveId}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            {`${vehicle.make} ${vehicle.model}`}
                            <span 
                              style={{fontSize: '0.8em',color: '#909090',cursor: 'pointer',userSelect: 'none'}}
                              onClick={() => copyToClipboard(vehicle.hiveId, setCopiedId)}
                              title="Copy vehicle ID"
                            >
                              {vehicle.hiveId}
                              {copiedId === vehicle.hiveId && ' ✓'}
                            </span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p style={{color: '#909090'}}>No vehicles owned</p>
                  )}
                </div>
              </div>
              <div style={{ 
                flex: 1,
                padding: '1rem',
                backgroundColor: '#2a2a2a',
                borderRadius: '4px'
              }}>
                <div>
                  <strong>Member of:</strong>
                  {person.memberOf.length > 0 ? (
                    <ul>
                      {person.memberOf.map(org => (
                        <li style={{marginLeft: '18px', color: '#ffda94'}} key={org.hiveId}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            {org.name}
                            <span 
                              style={{fontSize: '0.8em',color: '#909090',cursor: 'pointer',userSelect: 'none'}}
                              onClick={() => copyToClipboard(org.hiveId, setCopiedId)}
                              title="Click to copy Hive ID"
                            >
                              {org.hiveId}
                              {copiedId === org.hiveId && ' ✓'}
                            </span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p style={{color: '#909090'}}>Not a member of any organizations</p>
                  )}
                </div>
                <div>
                  <div style={{margin: '6px 0' }}></div>
                  <strong>Suspect in:</strong>
                  {person.suspectIn.length > 0 ? (
                    <ul>
                      {person.suspectIn.map(case_ => (
                        <li style={{marginLeft: '18px', color: '#ffda94'}} key={case_.hiveId}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            {case_.title}
                            <span 
                              style={{ 
                                fontSize: '0.8em', 
                                color: '#909090',
                                cursor: 'pointer',
                                userSelect: 'none'
                              }}
                              onClick={() => copyToClipboard(case_.hiveId, setCopiedId)}
                              title="Click to copy ID"
                            >
                              {case_.hiveId}
                              {copiedId === case_.hiveId && ' ✓'}
                            </span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p style={{color: '#909090'}}>Not a suspect in any cases</p>
                  )}
                </div>
                <div>
                  <div style={{margin: '6px 0' }}></div>
                  <strong>Victim in:</strong>
                  {person.victimIn.length > 0 ? (
                    <ul>
                      {person.victimIn.map(case_ => (
                        <li style={{marginLeft: '18px', color: '#ffda94'}} key={case_.hiveId}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            {case_.title}
                            <span 
                              style={{ 
                                fontSize: '0.8em', 
                                color: '#909090',
                                cursor: 'pointer',
                                userSelect: 'none'
                              }}
                              onClick={() => copyToClipboard(case_.hiveId, setCopiedId)}
                              title="Click to copy ID"
                            >
                              {case_.hiveId}
                              {copiedId === case_.hiveId && ' ✓'}
                            </span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p style={{color: '#909090'}}>Not a victim in any cases</p>
                  )}
                </div>
                <div>
                  <div style={{margin: '6px 0' }}></div>
                  <strong>Witness in:</strong>
                  {person.witnessIn.length > 0 ? (
                    <ul>
                      {person.witnessIn.map(case_ => (
                        <li style={{marginLeft: '18px', color: '#ffda94'}} key={case_.hiveId}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            {case_.title}
                            <span 
                              style={{ 
                                fontSize: '0.8em', 
                                color: '#909090',
                                cursor: 'pointer',
                                userSelect: 'none'
                              }}
                              onClick={() => copyToClipboard(case_.hiveId, setCopiedId)}
                              title="Click to copy ID"
                            >
                              {case_.hiveId}
                              {copiedId === case_.hiveId && ' ✓'}
                            </span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p style={{color: '#909090'}}>Not a witness in any cases</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
