import React, { useState } from 'react';
import { useQuery, useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { GET_OPERATIONS } from '../../../api/queries';
import { DELETE_OPERATION } from '../../../api/mutations';
import CreateOperationModal from './CreateOperationModal';
import ManageTargetsModal from './ManageTargetsModal';
import ManageCaseScopeModal from './ManageCaseScopeModal';
import AssignLeadAgentModal from './AssignLeadAgentModal';
import copyToClipboard from '../../utils/clipboard';
import '../../../css/shared.css';

export default function Operations() {
  const { loading, error, data, refetch } = useQuery(GET_OPERATIONS);
  const [copiedId, setCopiedId] = useState(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isManageTargetsModalOpen, setIsManageTargetsModalOpen] = useState(false);
  const [isManageCaseScopeModalOpen, setIsManageCaseScopeModalOpen] = useState(false);
  const [isAssignLeadAgentModalOpen, setIsAssignLeadAgentModalOpen] = useState(false);
  const [selectedOperation, setSelectedOperation] = useState(null);

  const [deleteOperationMutation] = useMutation(DELETE_OPERATION, {
    onCompleted: () => {
      refetch();
      toast.success('Operation deleted successfully!');
    },
    onError: (deleteError) => {
      console.error('Error deleting operation:', deleteError);
      toast.error(`Error deleting operation: ${deleteError.message}`);
    }
  });




  const openCreateModal = () => setIsCreateModalOpen(true);
  const closeCreateModal = () => setIsCreateModalOpen(false);

  const openManageTargetsModal = (operation) => {
    setSelectedOperation(operation);
    setIsManageTargetsModalOpen(true);
  };
  const closeManageTargetsModal = () => {
    setIsManageTargetsModalOpen(false);
    setSelectedOperation(null);
  };

  const openManageCaseScopeModal = (operation) => {
    setSelectedOperation(operation);
    setIsManageCaseScopeModalOpen(true);
  };
  const closeManageCaseScopeModal = () => {
    setIsManageCaseScopeModalOpen(false);
    setSelectedOperation(null);
  };

  const openAssignLeadAgentModal = (operation) => {
    setSelectedOperation(operation);
    setIsAssignLeadAgentModalOpen(true);
  };
  const closeAssignLeadAgentModal = () => {
    setIsAssignLeadAgentModalOpen(false);
    setSelectedOperation(null);
  };

  const handleDeleteOperation = (hiveId) => {
    if (window.confirm('Are you sure you want to delete this operation?')) {
      deleteOperationMutation({ variables: { hiveId } });
    }
  };



  const formatTargetName = (target) => {
    switch (target.__typename) {
      case 'Person':
        return `${target.firstName} ${target.lastName}`;
      case 'Organization':
        return target.name;
      case 'Vehicle':
        return `${target.make} ${target.model} (${target.color})`;
      default:
        return 'Unknown Target';
    }
  };

  if (loading) return <p>Loading operations...</p>;
  if (error) return <p>Error loading operations: {error.message}</p>;

  return (
    <div style={{ padding: '1rem' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h1>Operations</h1>
        <button onClick={openCreateModal} className="button-primary">
          Create Operation
        </button>
      </div>

      <CreateOperationModal isOpen={isCreateModalOpen} onClose={closeCreateModal} />
      {selectedOperation && (
        <>
          <ManageTargetsModal
            isOpen={isManageTargetsModalOpen}
            onClose={closeManageTargetsModal}
            operation={selectedOperation}
            onRefetch={refetch}
          />
          <ManageCaseScopeModal
            isOpen={isManageCaseScopeModalOpen}
            onClose={closeManageCaseScopeModal}
            operation={selectedOperation}
            onRefetch={refetch}
          />
          <AssignLeadAgentModal
            isOpen={isAssignLeadAgentModalOpen}
            onClose={closeAssignLeadAgentModal}
            operation={selectedOperation}
            onRefetch={refetch}
          />
        </>
      )}

      <div style={{ marginTop: '2rem' }}>
        {data.operations.map((operation) => (
          <div 
            key={operation.hiveId}
            style={{
              padding: '1rem',
              marginBottom: '1rem',
              borderRadius: '4px',
              backgroundColor: '#222',
              position: 'relative'
            }}
          >
            <div style={{ position: 'absolute', top: '12px', right: '16px', display: 'flex', gap: '10px' }}>
              <button
                onClick={() => openAssignLeadAgentModal(operation)}
                className="button-utility"
                title={operation.leadAgent && operation.leadAgent.length > 0 ? 'Change Lead Agent' : 'Assign Lead Agent'}
              >
                {operation.leadAgent && operation.leadAgent.length > 0 ? 'Change Lead' : 'Assign Lead'}
              </button>
              <button
                onClick={() => handleDeleteOperation(operation.hiveId)}
                className="button-delete"
                title="Delete Operation"
              >
                Delete
              </button>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginRight: '150px' }}>
              <h3 style={{ margin: 0, color: '#fff' }}>
                {operation.title}
              </h3>
              <span
                style={{
                  fontSize: '0.8em',
                  color: copiedId === operation.hiveId ? '#4CAF50' : '#909090',
                  cursor: 'pointer',
                  userSelect: 'none'
                }}
                onClick={() => copyToClipboard(operation.hiveId, setCopiedId)}
                title="Click to copy Hive ID"
              >
                {operation.hiveId}
                {copiedId === operation.hiveId && ' ✓'}
              </span>
            </div>
            <hr />
            <p style={{ margin: '0.25rem 0', color: '#ccc' }}>
              <strong>Type:</strong> {operation.type}
            </p>
            <p style={{ margin: '0.25rem 0', color: '#ccc' }}>
              <strong>Created:</strong> {new Date(operation.creationDate).toLocaleDateString()}
            </p>

            {/* Targets Section */}
            <div style={{ marginTop: '1rem' }}>
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
                <h4 style={{ margin: 0, color: '#fff' }}>Targets:</h4>
                <button
                  onClick={() => openManageTargetsModal(operation)}
                  className="button-utility"
                  title="Manage Targets"
                >
                  Manage Targets
                </button>
              </div>
              {operation.targets && operation.targets.length > 0 ? (
                <div style={{
                  margin: '0.5rem 0',
                  padding: '0.5rem',
                  backgroundColor: '#2a2a2a',
                  borderRadius: '4px',
                  border: '1px solid #444'
                }}>
                  {operation.targets.map((target) => (
                    <div key={target.hiveId} style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      padding: '0.25rem 0',
                      borderBottom: operation.targets.indexOf(target) < operation.targets.length - 1 ? '1px solid #333' : 'none'
                    }}>
                      <span style={{ color: '#e0e0e0', fontSize: '0.9rem' }}>
                        {formatTargetName(target)} (ID: {target.hiveId})
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <p style={{ color: '#888', margin: '0.5rem 0', fontStyle: 'italic' }}>No targets assigned</p>
              )}
            </div>

            {/* Lead Agent Section */}
            <div style={{ marginTop: '1rem' }}>
              <h4 style={{ margin: '0 0 0.5rem 0', color: '#fff' }}>Lead Agent:</h4>
              {operation.leadAgent && operation.leadAgent.length > 0 ? (
                <div style={{
                  margin: '0.5rem 0',
                  padding: '0.5rem',
                  backgroundColor: '#2a2a2a',
                  borderRadius: '4px',
                  border: '1px solid #444'
                }}>
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}>
                    <span style={{ color: '#e0e0e0', fontSize: '0.9rem' }}>
                      {operation.leadAgent[0].username}
                      ({operation.leadAgent[0].backingPerson[0]?.firstName} {operation.leadAgent[0].backingPerson[0]?.lastName})
                    </span>
                  </div>
                </div>
              ) : (
                <p style={{ color: '#888', margin: '0.5rem 0', fontStyle: 'italic' }}>No lead agent assigned</p>
              )}
            </div>

            {/* Case Scope Section */}
            <div style={{ marginTop: '1rem' }}>
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
                <h4 style={{ margin: 0, color: '#fff' }}>Case Scope:</h4>
                <button
                  onClick={() => openManageCaseScopeModal(operation)}
                  className="button-utility"
                  title="Manage Case Scope"
                >
                  Manage Cases
                </button>
              </div>
              {operation.scopedToCase && operation.scopedToCase.length > 0 ? (
                <div style={{
                  margin: '0.5rem 0',
                  padding: '0.5rem',
                  backgroundColor: '#2a2a2a',
                  borderRadius: '4px',
                  border: '1px solid #444'
                }}>
                  {operation.scopedToCase.map((caseItem) => (
                    <div key={caseItem.hiveId} style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      padding: '0.25rem 0',
                      borderBottom: operation.scopedToCase.indexOf(caseItem) < operation.scopedToCase.length - 1 ? '1px solid #333' : 'none'
                    }}>
                      <span style={{ color: '#e0e0e0', fontSize: '0.9rem' }}>
                        {caseItem.title} (Status: {caseItem.status})
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <p style={{ color: '#888', margin: '0.5rem 0', fontStyle: 'italic' }}>No cases in scope</p>
              )}
            </div>
            {/* Tasks Section */}
            <div style={{ marginTop: '1rem' }}>
              <h4 style={{ margin: '0 0 0.5rem 0', color: '#fff' }}>Tasks:</h4>
              {operation.scopedTasks && operation.scopedTasks.length > 0 ? (
                <div style={{
                  margin: '0.5rem 0',
                  padding: '0.5rem',
                  backgroundColor: '#2a2a2a',
                  borderRadius: '4px',
                  border: '1px solid #444'
                }}>
                  {operation.scopedTasks.map((task) => (
                    <div key={task.hiveId} style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      padding: '0.25rem 0',
                      borderBottom: operation.scopedTasks.indexOf(task) < operation.scopedTasks.length - 1 ? '1px solid #333' : 'none'
                    }}>
                      <span style={{ color: '#e0e0e0', fontSize: '0.9rem' }}>
                        {task.title} (CL: {task.level}, Prio: {task.priority})
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <p style={{ color: '#888', margin: '0.5rem 0', fontStyle: 'italic' }}>No tasks</p>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
