import React, { useState } from 'react';
import { useQuery, useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { GET_OPERATIONS } from '../../../api/queries';
import { DELETE_OPERATION } from '../../../api/mutations';
import CreateOperationModal from './CreateOperationModal';
import ManageTargetsModal from './ManageTargetsModal';
import ManageCaseScopeModal from './ManageCaseScopeModal';
import AssignLeadAgentModal from './AssignLeadAgentModal';
import copyToClipboard from '../../utils/clipboard';
import '../../../css/shared.css';

export default function Operations() {
  const { loading, error, data, refetch } = useQuery(GET_OPERATIONS);
  const [copiedId, setCopiedId] = useState(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isManageTargetsModalOpen, setIsManageTargetsModalOpen] = useState(false);
  const [isManageCaseScopeModalOpen, setIsManageCaseScopeModalOpen] = useState(false);
  const [isAssignLeadAgentModalOpen, setIsAssignLeadAgentModalOpen] = useState(false);
  const [selectedOperation, setSelectedOperation] = useState(null);

  const [deleteOperationMutation] = useMutation(DELETE_OPERATION, {
    onCompleted: () => {
      refetch();
      toast.success('Operation deleted successfully!');
    },
    onError: (deleteError) => {
      console.error('Error deleting operation:', deleteError);
      toast.error(`Error deleting operation: ${deleteError.message}`);
    }
  });




  const openCreateModal = () => setIsCreateModalOpen(true);
  const closeCreateModal = () => setIsCreateModalOpen(false);

  const openManageTargetsModal = (operation) => {
    setSelectedOperation(operation);
    setIsManageTargetsModalOpen(true);
  };
  const closeManageTargetsModal = () => {
    setIsManageTargetsModalOpen(false);
    setSelectedOperation(null);
  };

  const openManageCaseScopeModal = (operation) => {
    setSelectedOperation(operation);
    setIsManageCaseScopeModalOpen(true);
  };
  const closeManageCaseScopeModal = () => {
    setIsManageCaseScopeModalOpen(false);
    setSelectedOperation(null);
  };

  const openAssignLeadAgentModal = (operation) => {
    setSelectedOperation(operation);
    setIsAssignLeadAgentModalOpen(true);
  };
  const closeAssignLeadAgentModal = () => {
    setIsAssignLeadAgentModalOpen(false);
    setSelectedOperation(null);
  };

  const handleDeleteOperation = (hiveId) => {
    if (window.confirm('Are you sure you want to delete this operation?')) {
      deleteOperationMutation({ variables: { hiveId } });
    }
  };



  const formatTargetName = (target) => {
    switch (target.__typename) {
      case 'Person':
        return `${target.firstName} ${target.lastName}`;
      case 'Organization':
        return target.name;
      case 'Vehicle':
        return `${target.make} ${target.model} (${target.color})`;
      default:
        return 'Unknown Target';
    }
  };

  if (loading) return <p>Loading operations...</p>;
  if (error) return <p>Error loading operations: {error.message}</p>;

  return (
    <div style={{ padding: '1rem' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h1>Operations</h1>
        <button onClick={openCreateModal} className="button-primary">
          Create Operation
        </button>
      </div>

      <CreateOperationModal isOpen={isCreateModalOpen} onClose={closeCreateModal} />
      {selectedOperation && (
        <>
          <ManageTargetsModal
            isOpen={isManageTargetsModalOpen}
            onClose={closeManageTargetsModal}
            operation={selectedOperation}
            onRefetch={refetch}
          />
          <ManageCaseScopeModal
            isOpen={isManageCaseScopeModalOpen}
            onClose={closeManageCaseScopeModal}
            operation={selectedOperation}
            onRefetch={refetch}
          />
          <AssignLeadAgentModal
            isOpen={isAssignLeadAgentModalOpen}
            onClose={closeAssignLeadAgentModal}
            operation={selectedOperation}
            onRefetch={refetch}
          />
        </>
      )}

      <div style={{ marginTop: '2rem' }}>
        {data.operations.map((operation) => (
          <div 
            key={operation.hiveId}
            style={{
              padding: '1rem',
              marginBottom: '1rem',
              borderRadius: '4px',
              backgroundColor: '#222',
              position: 'relative'
            }}
          >
            <div style={{ position: 'absolute', top: '12px', right: '16px', display: 'flex', gap: '10px' }}>
              <button
                onClick={() => openManageTargetsModal(operation)}
                className="button-utility"
                title="Manage Targets"
              >
                Targets
              </button>
              <button
                onClick={() => openManageCaseScopeModal(operation)}
                className="button-utility"
                title="Manage Case Scope"
              >
                Cases
              </button>
              <button
                onClick={() => openAssignLeadAgentModal(operation)}
                className="button-utility"
                title={operation.leadAgent && operation.leadAgent.length > 0 ? 'Change Lead Agent' : 'Assign Lead Agent'}
              >
                {operation.leadAgent && operation.leadAgent.length > 0 ? 'Change Lead' : 'Assign Lead'}
              </button>
              <button
                onClick={() => handleDeleteOperation(operation.hiveId)}
                className="button-delete"
                title="Delete Operation"
              >
                Delete
              </button>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginRight: '300px' }}>
              <h3 style={{ margin: 0, color: '#fff' }}>
                {operation.title}
              </h3>
              <span
                style={{
                  fontSize: '0.8em',
                  color: copiedId === operation.hiveId ? '#4CAF50' : '#909090',
                  cursor: 'pointer',
                  userSelect: 'none'
                }}
                onClick={() => copyToClipboard(operation.hiveId, setCopiedId)}
                title="Click to copy Hive ID"
              >
                {operation.hiveId}
                {copiedId === operation.hiveId && ' ✓'}
              </span>
            </div>
            <hr />
            <div style={{ display: 'flex', gap: '1rem' }}>
              <div style={{
                flex: 1,
                padding: '1rem',
                backgroundColor: '#2a2a2a',
                borderRadius: '4px'
              }}>
                <div>
                  <strong>Type:</strong> <p style={{color: '#909090'}}>{operation.type}</p>
                </div>
                <div style={{margin: '6px 0' }}></div>
                <div>
                  <strong>Created:</strong> <p style={{color: '#909090'}}>
                    {new Date(operation.creationDate).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: '2-digit' })}
                  </p>
                </div>
              </div>
              <div style={{
                flex: 1,
                padding: '1rem',
                backgroundColor: '#2a2a2a',
                borderRadius: '4px'
              }}>
                <div>
                  <div>
                    <strong>Targets:</strong>
                  </div>
                  {operation.targets && operation.targets.length > 0 ? (
                    <ul>
                      {operation.targets.map((target) => (
                        <li style={{marginLeft: '18px', color: '#ffda94'}} key={target.hiveId}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            {formatTargetName(target)}
                            <span
                              style={{fontSize: '0.8em',color: '#909090',cursor: 'pointer',userSelect: 'none'}}
                              onClick={() => copyToClipboard(target.hiveId, setCopiedId)}
                              title="Click to copy Hive ID"
                            >
                              {target.hiveId}{copiedId === target.hiveId && ' ✓'}
                            </span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p style={{color: '#909090'}}>No targets assigned</p>
                  )}
                </div>
                <div>
                  <div style={{margin: '6px 0' }}></div>
                  <div>
                    <strong>Lead Agent:</strong>
                  </div>
                  {operation.leadAgent && operation.leadAgent.length > 0 ? (
                    <ul>
                      {operation.leadAgent.map((agent) => (
                        <li style={{marginLeft: '18px', color: '#ffda94'}} key={agent.hiveId}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            {agent.username} ({agent.backingPerson[0]?.firstName} {agent.backingPerson[0]?.lastName})
                            <span
                              style={{fontSize: '0.8em',color: '#909090',cursor: 'pointer',userSelect: 'none'}}
                              onClick={() => copyToClipboard(agent.hiveId, setCopiedId)}
                              title="Click to copy Hive ID"
                            >
                              {agent.hiveId}{copiedId === agent.hiveId && ' ✓'}
                            </span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p style={{color: '#909090'}}>No lead agent assigned</p>
                  )}
                </div>
              </div>
              <div style={{
                flex: 1,
                padding: '1rem',
                backgroundColor: '#2a2a2a',
                borderRadius: '4px'
              }}>
                <div>
                  <div>
                    <strong>Case Scope:</strong>
                  </div>
                  {operation.scopedToCase && operation.scopedToCase.length > 0 ? (
                    <ul>
                      {operation.scopedToCase.map((caseItem) => (
                        <li style={{marginLeft: '18px', color: '#ffda94'}} key={caseItem.hiveId}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            {caseItem.title} ({caseItem.status})
                            <span
                              style={{fontSize: '0.8em',color: '#909090',cursor: 'pointer',userSelect: 'none'}}
                              onClick={() => copyToClipboard(caseItem.hiveId, setCopiedId)}
                              title="Click to copy Hive ID"
                            >
                              {caseItem.hiveId}{copiedId === caseItem.hiveId && ' ✓'}
                            </span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p style={{color: '#909090'}}>No cases in scope</p>
                  )}
                </div>
                <div>
                  <div style={{margin: '6px 0' }}></div>
                  <div>
                    <strong>Tasks:</strong>
                  </div>
                  {operation.scopedTasks && operation.scopedTasks.length > 0 ? (
                    <ul>
                      {operation.scopedTasks.map((task) => (
                        <li style={{marginLeft: '18px', color: '#ffda94'}} key={task.hiveId}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            {task.title} (CL: {task.level}, Prio: {task.priority})
                            <span
                              style={{fontSize: '0.8em',color: '#909090',cursor: 'pointer',userSelect: 'none'}}
                              onClick={() => copyToClipboard(task.hiveId, setCopiedId)}
                              title="Click to copy Hive ID"
                            >
                              {task.hiveId}{copiedId === task.hiveId && ' ✓'}
                            </span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p style={{color: '#909090'}}>No tasks</p>
                  )}
                </div>
              </div>
            </div>


          </div>
        ))}
      </div>
    </div>
  );
}
