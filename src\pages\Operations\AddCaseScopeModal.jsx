import React, { useState } from 'react';
import { useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { ADD_SCOPED_CASE_TO_OPERATION } from '../../../api/mutations';
import styles from '../../../css/modal.module.css';

const AddCaseScopeModal = ({ isOpen, onClose, operation, onRefetch }) => {
  const [caseHiveId, setCaseHiveId] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  const [addCaseScopeMutation, { loading }] = useMutation(ADD_SCOPED_CASE_TO_OPERATION, {
    onCompleted: () => {
      onRefetch();
      onClose();
      setCaseHiveId('');
      setErrorMessage('');
      toast.success('Case added to operation scope successfully!');
    },
    onError: (error) => {
      setErrorMessage(error.message || 'Could not add case to operation scope. Please try again.');
      toast.error(error.message || 'Could not add case to operation scope. Please try again.');
    }
  });

  if (!isOpen || !operation) return null;

  const handleSubmit = (e) => {
    e.preventDefault();
    setErrorMessage('');
    
    if (!caseHiveId.trim()) {
      setErrorMessage('Case Hive ID is required.');
      return;
    }

    // Check if case is already in the operation scope
    const isAlreadyInScope = operation.scopedToCase?.some(caseItem => caseItem.hiveId === caseHiveId.trim());
    if (isAlreadyInScope) {
      setErrorMessage('This case is already in the operation scope.');
      return;
    }

    addCaseScopeMutation({
      variables: {
        operationHiveId: operation.hiveId,
        caseHiveId: caseHiveId.trim()
      }
    });
  };

  const handleClose = () => {
    setCaseHiveId('');
    setErrorMessage('');
    onClose();
  };

  return (
    <div className={styles.modalBackdrop} onClick={handleClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.modalCloseButton} onClick={handleClose}>
          &times;
        </button>
        <div className={styles.modalHeader}>
          <h3>Add Case to Operation Scope</h3>
          <p style={{ fontSize: '0.9rem', color: '#ccc', margin: '0.5rem 0 0 0' }}>
            Operation: {operation.title}
          </p>
        </div>
        <form onSubmit={handleSubmit}>
          <div className={styles.modalBody}>
            {errorMessage && <p className={styles.errorMessage}>{errorMessage}</p>}
            
            <div className={styles.formGroup}>
              <label htmlFor="caseHiveId">Case Hive ID:</label>
              <input
                type="text"
                id="caseHiveId"
                className={styles.darkInput}
                value={caseHiveId}
                onChange={(e) => setCaseHiveId(e.target.value)}
                placeholder="Enter Case Hive ID"
                required
              />
              <small style={{ color: '#888', fontSize: '0.8rem' }}>
                Enter the Hive ID of a Case to add to this operation's scope.
              </small>
            </div>

            {/* Show current scoped cases for reference */}
            {operation.scopedToCase && operation.scopedToCase.length > 0 && (
              <div style={{ marginTop: '1rem' }}>
                <label style={{ color: '#ccc', fontSize: '0.9rem' }}>Current Cases in Scope:</label>
                <ul style={{ margin: '0.5rem 0', paddingLeft: '1rem', fontSize: '0.8rem', color: '#888' }}>
                  {operation.scopedToCase.map((caseItem) => (
                    <li key={caseItem.hiveId}>
                      {caseItem.title} (Status: {caseItem.status}, ID: {caseItem.hiveId})
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
          <div className={styles.modalFooter}>
            <button type="button" className={styles.secondary} onClick={handleClose}>
              Cancel
            </button>
            <button type="submit" className={styles.primary} disabled={loading}>
              {loading ? 'Adding...' : 'Add Case'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddCaseScopeModal;
