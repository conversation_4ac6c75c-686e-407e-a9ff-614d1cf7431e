{"name": "hive", "version": "1.0.0", "main": "server.js", "dependencies": {"@apollo/server": "^4.10.1", "@neo4j/graphql": "^7.0.0", "bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "graphql-tag": "^2.12.6", "jsonwebtoken": "^9.0.2", "neo4j-driver": "^5.28.1"}, "scripts": {"start": "node server.js", "test": "jest", "generate-token": "node scripts/generate-token.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"jest": "^30.0.3"}}