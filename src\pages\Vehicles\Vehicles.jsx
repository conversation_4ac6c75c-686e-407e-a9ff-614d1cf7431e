import React, { useState } from 'react';
import { useQuery, useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { GET_VEHICLES, GET_PEOPLE, GET_ORGANIZATIONS } from '../../../api/queries';
import { CREATE_VEHICLE, DELETE_VEHICLE, SET_VEHICLE_OWNER } from '../../../api/mutations';
import CreateVehicleModal from './CreateVehicleModal';
import SetVehicleOwnerModal from './SetVehicleOwnerModal';
import copyToClipboard from '../../utils/clipboard';
import '../../../css/shared.css';

const Vehicles = () => {
  const { loading, error, data, refetch } = useQuery(GET_VEHICLES);

  const [createVehicleMutation] = useMutation(CREATE_VEHICLE, {
    onCompleted: () => {
      refetch(); // Refetch vehicles list after creation
      setIsModalOpen(false);
      toast.success('Vehicle created successfully!');
    },
    onError: (createError) => {
      console.error('Error creating vehicle:', createError);
      toast.error(`Error creating vehicle: ${createError.message}`);
    }
  });
  const [deleteVehicleMutation] = useMutation(DELETE_VEHICLE, {
    onCompleted: () => {
      refetch();
      toast.success('Vehicle deleted successfully!');
    },
    onError: (deleteError) => {
      console.error('Error deleting vehicle:', deleteError);
      toast.error(`Error deleting vehicle: ${deleteError.message}`);
    }
  });
  const [setVehicleOwnerMutation] = useMutation(SET_VEHICLE_OWNER, {
    onCompleted: () => {
      refetch();
      setIsSetOwnerModalOpen(false);
      setCurrentVehicle(null);
      toast.success('Vehicle owner set successfully!');
    },
    onError: (setError) => {
      console.error('Error setting vehicle owner:', setError);
      toast.error(`Error setting vehicle owner: ${setError.message}`);
    }
  });

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSetOwnerModalOpen, setIsSetOwnerModalOpen] = useState(false);
  const [currentVehicle, setCurrentVehicle] = useState(null);
  const [copiedId, setCopiedId] = useState(null);

  const handleDeleteVehicle = (vehicleId, vehicleName) => {
    if (window.confirm(`Are you sure you want to delete ${vehicleName} (ID: ${vehicleId})? This action cannot be undone.`)) {
      deleteVehicleMutation({ variables: { hiveId: vehicleId } });
    }
  };

  const openSetOwnerModal = (vehicle) => {
    setCurrentVehicle(vehicle);
    setIsSetOwnerModalOpen(true);
  };


  if (loading) return <div style={{ padding: '1rem' }}>Loading vehicles...</div>;
  if (error) return <div style={{ padding: '1rem' }}>Error: {error.message}</div>;

  const openCreateModal = () => setIsModalOpen(true);
  const closeCreateModal = () => setIsModalOpen(false);

  const closeSetOwnerModal = () => {
    setIsSetOwnerModalOpen(false);
    setCurrentVehicle(null);
  };

  return (
    <div style={{ padding: '1rem' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h1>Vehicles</h1>
        <button onClick={openCreateModal} className="button-primary">
          Create Vehicle
        </button>
      </div>

      <CreateVehicleModal 
        isOpen={isModalOpen} 
        onClose={closeCreateModal}
        onSubmit={(formData) => createVehicleMutation({ variables: formData })}
      />
      
      {currentVehicle && (
        <SetVehicleOwnerModal 
          isOpen={isSetOwnerModalOpen} 
          onClose={closeSetOwnerModal} 
          vehicle={currentVehicle}
          onSubmitSetOwner={(ownerData) => setVehicleOwnerMutation({
            variables: {
              vehicleHiveId: currentVehicle.hiveId,
              ownerHiveId: ownerData.ownerHiveId
            }
          })}
        />
      )}

      <div style={{ marginTop: '2rem' }}>
        {data && data.vehicles && data.vehicles.length > 0 ? (
          data.vehicles.map((vehicle) => (
            <div 
              key={vehicle.hiveId}
              style={{
                padding: '1rem',
                marginBottom: '1rem',
                borderRadius: '4px',
                backgroundColor: '#222',
                position: 'relative'
              }}
            >
              <div style={{ position: 'absolute', top: '12px', right: '16px', display: 'flex', gap: '10px' }}>
                <button
                  onClick={() => openSetOwnerModal(vehicle)}
                  className="button-utility"
                  title="Set Vehicle Owner"
                >
                  Set Owner
                </button>
                <button
                  onClick={() => handleDeleteVehicle(vehicle.hiveId, `${vehicle.make} ${vehicle.model}`)}
                  className="button-delete"
                  title="Delete Vehicle"
                >
                  Delete
                </button>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginRight: '150px' }}>
                <h3>{`${vehicle.make} ${vehicle.model}`}</h3>
                <span 
                  style={{ fontSize: '0.8em', color: '#909090', cursor: 'pointer', userSelect: 'none' }}
                  onClick={() => copyToClipboard(vehicle.hiveId, setCopiedId)}
                  title="Click to copy Vehicle ID"
                >
                  {vehicle.hiveId}
                  {copiedId === vehicle.hiveId && ' ✓'}
                </span>
              </div>
              <hr />
              <div style={{ display: 'flex', gap: '1rem' }}>
                <div style={{ flex: 1, padding: '1rem', backgroundColor: '#2a2a2a', borderRadius: '4px' }}>
                  <div style={{marginBottom: '6px'}}>
                    <strong>Type:</strong> <p style={{color: '#909090'}}>{vehicle.type || 'Not specified'}</p>
                  </div>
                  <div style={{marginBottom: '6px'}}>
                    <strong>Color:</strong> <p style={{color: '#909090'}}>{vehicle.color || 'Not specified'}</p>
                  </div>
                </div>
                <div style={{ flex: 1, padding: '1rem', backgroundColor: '#2a2a2a', borderRadius: '4px' }}>
                  <div>
                    <strong>Owner(s):</strong>
                    {vehicle.owner && vehicle.owner.length > 0 ? (
                      <ul>
                        {vehicle.owner.map(o => {
                          if (!o || !o.hiveId) return null;
                          const ownerName = o.__typename === 'Person' ? `${o.firstName} ${o.lastName}` : o.name;
                          return (
                            <li style={{marginLeft: '18px', color: '#ffda94'}} key={o.hiveId}>
                              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                <span>{o.__typename}: {ownerName}</span>
                                <span 
                                  style={{fontSize: '0.8em',color: '#909090',cursor: 'pointer',userSelect: 'none'}}
                                  onClick={() => copyToClipboard(o.hiveId, setCopiedId)}
                                  title={`Copy ${o.__typename} ID`}
                                >
                                  {o.hiveId}
                                  {copiedId === o.hiveId && ' ✓'}
                                </span>
                              </div>
                            </li>
                          );
                        })}
                      </ul>
                    ) : (
                      <p style={{color: '#909090'}}>N/A</p>
                    )}
                  </div>
                </div>
              </div>
              <div style={{ marginTop: '1rem', textAlign: 'right'}}>

              </div>
            </div>
          ))
        ) : (
          <p style={{color: '#909090', marginTop: '2rem' }}>No vehicles found.</p> 
        )}
      </div>
    </div>
  );
};

export default Vehicles; 