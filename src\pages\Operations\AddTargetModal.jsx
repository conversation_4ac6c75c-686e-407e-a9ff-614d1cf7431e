import React, { useState } from 'react';
import { useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { ADD_TARGET_TO_OPERATION } from '../../../api/mutations';
import styles from '../../../css/modal.module.css';

const AddTargetModal = ({ isOpen, onClose, operation, onRefetch }) => {
  const [targetHiveId, setTargetHiveId] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  const [addTargetMutation, { loading }] = useMutation(ADD_TARGET_TO_OPERATION, {
    onCompleted: () => {
      onRefetch();
      onClose();
      setTargetHiveId('');
      setErrorMessage('');
      toast.success('Target added to operation successfully!');
    },
    onError: (error) => {
      setErrorMessage(error.message || 'Could not add target to operation. Please try again.');
      toast.error(error.message || 'Could not add target to operation. Please try again.');
    }
  });

  if (!isOpen || !operation) return null;

  const handleSubmit = (e) => {
    e.preventDefault();
    setErrorMessage('');
    
    if (!targetHiveId.trim()) {
      setErrorMessage('Target Hive ID is required.');
      return;
    }

    // Check if target is already in the operation
    const isAlreadyTarget = operation.targets?.some(target => target.hiveId === targetHiveId.trim());
    if (isAlreadyTarget) {
      setErrorMessage('This target is already assigned to this operation.');
      return;
    }

    addTargetMutation({
      variables: {
        targetHiveId: targetHiveId.trim(),
        operationHiveId: operation.hiveId
      }
    });
  };

  const handleClose = () => {
    setTargetHiveId('');
    setErrorMessage('');
    onClose();
  };

  return (
    <div className={styles.modalBackdrop} onClick={handleClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.modalCloseButton} onClick={handleClose}>
          &times;
        </button>
        <div className={styles.modalHeader}>
          <h3>Add Target to Operation</h3>
          <p style={{ fontSize: '0.9rem', color: '#ccc', margin: '0.5rem 0 0 0' }}>
            Operation: {operation.title}
          </p>
        </div>
        <form onSubmit={handleSubmit}>
          <div className={styles.modalBody}>
            {errorMessage && <p className={styles.errorMessage}>{errorMessage}</p>}
            
            <div className={styles.formGroup}>
              <label htmlFor="targetHiveId">Target Hive ID:</label>
              <input
                type="text"
                id="targetHiveId"
                className={styles.darkInput}
                value={targetHiveId}
                onChange={(e) => setTargetHiveId(e.target.value)}
                placeholder="Enter Person, Organization, or Vehicle Hive ID"
                required
              />
              <small style={{ color: '#888', fontSize: '0.8rem' }}>
                Enter the Hive ID of a Person, Organization, or Vehicle to add as a target.
              </small>
            </div>

            {/* Show current targets for reference */}
            {operation.targets && operation.targets.length > 0 && (
              <div style={{ marginTop: '1rem' }}>
                <label style={{ color: '#ccc', fontSize: '0.9rem' }}>Current Targets:</label>
                <ul style={{ margin: '0.5rem 0', paddingLeft: '1rem', fontSize: '0.8rem', color: '#888' }}>
                  {operation.targets.map((target) => (
                    <li key={target.hiveId}>
                      {target.__typename === 'Person' && `${target.firstName} ${target.lastName}`}
                      {target.__typename === 'Organization' && target.name}
                      {target.__typename === 'Vehicle' && `${target.make} ${target.model} (${target.color})`}
                      {' '}(ID: {target.hiveId})
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
          <div className={styles.modalFooter}>
            <button type="button" className={styles.secondary} onClick={handleClose}>
              Cancel
            </button>
            <button type="submit" className={styles.primary} disabled={loading}>
              {loading ? 'Adding...' : 'Add Target'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddTargetModal;
